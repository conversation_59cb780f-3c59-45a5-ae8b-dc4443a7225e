"""
从节点索引构建器
实现从规则缓存文件构建索引的逻辑，支持热重载

主要功能：
1. 从rules_cache.json.gz文件解析规则数据
2. 使用与主节点相同的索引构建逻辑
3. 支持规则文件更新后的热重载
4. 实现文件监控和自动重建机制

技术特点：
- 统一构建逻辑：与主节点使用相同的RuleIndexManager.build_indexes_from_rule_details()
- 数据一致性：直接从规则数据构建，避免索引传输风险
- 热重载支持：文件更新后自动重建索引
- 容错性：构建失败时保留旧索引，不影响服务
"""

import gzip
import json
import os
import time
from typing import Any
from unittest.mock import Mock

from core.logging.logging_system import log as logger


class SlaveNodeIndexBuilder:
    """从节点索引构建器"""

    def __init__(self, rule_index_manager, rules_cache_path: str = "rules_cache.json.gz"):
        self.rule_index_manager = rule_index_manager
        self.rules_cache_path = rules_cache_path

    def build_index_from_cache_file(self) -> bool:
        """从规则缓存文件构建索引"""
        try:
            start_time = time.perf_counter()

            # 1. 检查文件是否存在
            if not os.path.exists(self.rules_cache_path):
                logger.error(f"规则缓存文件不存在: {self.rules_cache_path}")
                return False

            # 2. 加载规则数据
            rule_details = self._load_rule_details_from_cache()

            if not rule_details:
                logger.warning("规则缓存文件为空，无法构建索引")
                return False

            # 3. 构建索引（使用与主节点相同的逻辑）
            logger.info(f"从节点开始构建索引，规则数量: {len(rule_details)}")
            self.rule_index_manager.build_indexes_from_rule_details(rule_details)

            build_time = time.perf_counter() - start_time
            logger.info(f"从节点索引构建完成，耗时: {build_time*1000:.2f}ms")
            return True

        except Exception as e:
            logger.error(f"从节点索引构建失败: {e}", exc_info=True)
            return False

    def _load_rule_details_from_cache(self) -> list[Any]:
        """从压缩缓存文件加载规则明细"""
        try:
            logger.info(f"开始加载规则缓存文件: {self.rules_cache_path}")

            with gzip.open(self.rules_cache_path, 'rt', encoding='utf-8') as f:
                cache_data = json.load(f)

            # 转换为RuleDetail对象（或Mock对象）
            rule_details = []
            rule_data_list = cache_data.get('rule_details', [])

            if not rule_data_list:
                # 尝试其他可能的数据结构
                if 'rules' in cache_data:
                    rule_data_list = cache_data['rules']
                elif isinstance(cache_data, list):
                    rule_data_list = cache_data

            for rule_data in rule_data_list:
                rule_detail = self._create_rule_detail_from_dict(rule_data)
                if rule_detail:
                    rule_details.append(rule_detail)

            logger.info(f"成功加载{len(rule_details)}条规则数据")
            return rule_details

        except Exception as e:
            logger.error(f"加载规则缓存文件失败: {e}", exc_info=True)
            return []

    def _create_rule_detail_from_dict(self, rule_data: dict) -> Any:
        """从字典数据创建RuleDetail对象"""
        try:
            # 验证必要字段
            if not rule_data.get('rule_id'):
                logger.warning(f"规则数据缺少rule_id字段: {rule_data}")
                return None

            # 创建Mock对象模拟RuleDetail
            rule_detail = Mock()
            rule_detail.rule_id = rule_data.get('rule_id')
            rule_detail.yb_code = rule_data.get('yb_code', '')
            rule_detail.diag_whole_code = rule_data.get('diag_whole_code', '')
            rule_detail.diag_code_prefix = rule_data.get('diag_code_prefix', '')
            rule_detail.fee_whole_code = rule_data.get('fee_whole_code', '')
            rule_detail.fee_code_prefix = rule_data.get('fee_code_prefix', '')
            rule_detail.extended_fields = rule_data.get('extended_fields', '{}')

            # 确保extended_fields是字符串格式
            if isinstance(rule_detail.extended_fields, dict):
                rule_detail.extended_fields = json.dumps(rule_detail.extended_fields, ensure_ascii=False)

            return rule_detail

        except Exception as e:
            logger.error(f"创建规则对象失败: {e}, 数据: {rule_data}")
            return None


class IndexHotReloader:
    """索引热重载器"""

    def __init__(self, rule_index_manager, slave_builder: SlaveNodeIndexBuilder):
        self.rule_index_manager = rule_index_manager
        self.slave_builder = slave_builder
        self._file_watcher = None
        self._last_reload_time = 0
        self._reload_cooldown = 5  # 5秒冷却时间，避免频繁重载

    def start_watching(self):
        """开始监控规则文件变化"""
        try:
            from watchdog.events import FileSystemEventHandler
            from watchdog.observers import Observer

            class RuleCacheHandler(FileSystemEventHandler):
                def __init__(self, reloader):
                    self.reloader = reloader

                def on_modified(self, event):
                    if event.src_path.endswith('rules_cache.json.gz'):
                        current_time = time.time()
                        if current_time - self.reloader._last_reload_time > self.reloader._reload_cooldown:
                            logger.info("检测到规则缓存文件更新，开始重建索引")
                            self.reloader.reload_index()
                            self.reloader._last_reload_time = current_time

            self._file_watcher = Observer()
            self._file_watcher.schedule(
                RuleCacheHandler(self), 
                path=os.path.dirname(os.path.abspath(self.slave_builder.rules_cache_path)),
                recursive=False
            )
            self._file_watcher.start()
            logger.info("索引热重载监控已启动")

        except ImportError:
            logger.warning("watchdog库未安装，无法启用热重载功能")
        except Exception as e:
            logger.error(f"启动文件监控失败: {e}")

    def stop_watching(self):
        """停止监控"""
        if self._file_watcher:
            self._file_watcher.stop()
            self._file_watcher.join()
            logger.info("索引热重载监控已停止")

    def reload_index(self):
        """重新加载索引"""
        try:
            logger.info("开始索引热重载...")

            # 构建新索引
            success = self.slave_builder.build_index_from_cache_file()

            if success:
                logger.info("索引热重载成功")
            else:
                logger.error("索引热重载失败，保持原有索引")

        except Exception as e:
            logger.error(f"索引热重载异常: {e}", exc_info=True)


# 全局实例
slave_index_builder = None
index_hot_reloader = None


def initialize_slave_index_builder(rule_index_manager, rules_cache_path: str = "rules_cache.json.gz"):
    """初始化从节点索引构建器"""
    global slave_index_builder, index_hot_reloader

    slave_index_builder = SlaveNodeIndexBuilder(rule_index_manager, rules_cache_path)
    index_hot_reloader = IndexHotReloader(rule_index_manager, slave_index_builder)

    return slave_index_builder, index_hot_reloader


def build_slave_index(
        rule_index_manager,
        rules_cache_path: str = "rules_cache.json.gz", 
        enable_hot_reload: bool = True
    ) -> bool:
    """构建从节点索引的便捷函数"""
    builder, reloader = initialize_slave_index_builder(rule_index_manager, rules_cache_path)

    # 构建索引
    success = builder.build_index_from_cache_file()

    # 启动热重载（如果需要）
    if success and enable_hot_reload:
        reloader.start_watching()

    return success
