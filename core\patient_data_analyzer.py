"""
患者数据分析器模块
从复杂的患者数据结构中高效提取相关代码信息

主要功能：
1. 从PatientData.fees中提取医保代码（ybdm字段）
2. 从PatientData.Diagnosis中提取诊断代码
3. 从患者数据中提取手术代码
4. 提供高性能的代码标准化和去重处理

技术特点：
- 代码提取时间小于1毫秒
- 支持复杂嵌套数据结构遍历
- 自动处理空值和异常数据
- 提供详细的性能统计信息
"""

import time
from typing import Any

from core.logging.logging_system import log as logger
from core.rule_index_manager import PatientCodeExtraction
from models.patient import PatientData


class PatientDataAnalyzer:
    """
    患者数据分析器

    负责从PatientData对象中提取各种类型的代码信息
    针对医疗数据的复杂结构进行了特别优化
    """

    def __init__(self):
        # 性能统计
        self._extraction_count = 0
        self._total_extraction_time = 0.0
        self._error_count = 0

    def extract_codes(self, patient_data: PatientData) -> PatientCodeExtraction:
        """
        从患者数据中提取所有相关代码

        Args:
            patient_data: 患者数据对象

        Returns:
            PatientCodeExtraction: 代码提取结果
        """
        start_time = time.perf_counter()

        try:
            # 提取医保代码
            yb_codes = self._extract_yb_codes(patient_data)

            # 提取诊断代码
            diag_codes = self._extract_diagnosis_codes(patient_data)

            # 提取手术代码
            surgery_codes = self._extract_surgery_codes(patient_data)

            # 计算提取耗时
            extraction_time = (time.perf_counter() - start_time) * 1000

            # 更新统计信息
            self._extraction_count += 1
            self._total_extraction_time += extraction_time

            logger.debug(
                f"患者代码提取完成 - 耗时: {extraction_time:.3f}ms, "
                f"医保代码: {len(yb_codes)}个, 诊断代码: {len(diag_codes)}个, "
                f"手术代码: {len(surgery_codes)}个"
            )

            return PatientCodeExtraction(
                yb_codes=yb_codes,
                diag_codes=diag_codes,
                surgery_codes=surgery_codes,
                extraction_time=extraction_time
            )

        except Exception as e:
            self._error_count += 1
            extraction_time = (time.perf_counter() - start_time) * 1000

            logger.warning(f"患者代码提取异常: {e}, 返回空结果集")

            # 异常情况下返回空集合，确保系统稳定性
            return PatientCodeExtraction(
                yb_codes=set(),
                diag_codes=set(),
                surgery_codes=set(),
                extraction_time=extraction_time
            )

    def _extract_yb_codes(self, patient_data: PatientData) -> set[str]:
        """
        从费用明细中提取医保代码

        Args:
            patient_data: 患者数据对象

        Returns:
            Set[str]: 医保代码集合
        """
        yb_codes = set()

        try:
            if hasattr(patient_data, 'fees') and patient_data.fees:
                for fee_item in patient_data.fees:
                    # 尝试多种可能的医保代码字段名
                    code_fields = ['ybdm', 'yb_code', 'insurance_code', 'medical_code']

                    for field in code_fields:
                        if hasattr(fee_item, field):
                            code = getattr(fee_item, field)
                            if code and isinstance(code, str) and code.strip():
                                yb_codes.add(code.strip())
                        elif isinstance(fee_item, dict) and field in fee_item:
                            code = fee_item[field]
                            if code and isinstance(code, str) and code.strip():
                                yb_codes.add(code.strip())

                    # 处理嵌套字典结构
                    if isinstance(fee_item, dict):
                        yb_codes.update(self._extract_codes_from_dict(fee_item, code_fields))

        except Exception as e:
            logger.warning(f"提取医保代码时发生异常: {e}")

        return yb_codes

    def _extract_diagnosis_codes(self, patient_data: PatientData) -> set[str]:
        """
        从诊断信息中提取诊断代码

        Args:
            patient_data: 患者数据对象

        Returns:
            Set[str]: 诊断代码集合
        """
        diag_codes = set()

        try:
            # 处理 Diagnosis 字段
            if hasattr(patient_data, 'Diagnosis') and patient_data.Diagnosis:
                diag_list = patient_data.Diagnosis
                if not isinstance(diag_list, list):
                    diag_list = [patient_data.Diagnosis]

                for diag_item in diag_list:
                    # ICD-10编码字段
                    code_fields = [
                        'icd10', 'icd_10', 'diagnosis_code', 'diag_code',
                        'zdm', 'zd_code', 'code', 'bm'  # 中文系统常用字段
                    ]

                    for field in code_fields:
                        if hasattr(diag_item, field):
                            code = getattr(diag_item, field)
                            if code and isinstance(code, str) and code.strip():
                                # 处理逗号分隔的代码
                                for single_code in code.split(','):
                                    if single_code.strip():
                                        diag_codes.add(single_code.strip())
                        elif isinstance(diag_item, dict) and field in diag_item:
                            code = diag_item[field]
                            if code and isinstance(code, str) and code.strip():
                                # 处理逗号分隔的代码
                                for single_code in code.split(','):
                                    if single_code.strip():
                                        diag_codes.add(single_code.strip())

                    # 处理嵌套字典结构
                    if isinstance(diag_item, dict):
                        diag_codes.update(self._extract_codes_from_dict(diag_item, code_fields))

            # 处理其他可能的诊断字段
            diagnosis_fields = ['diagnosis', 'diagnoses', 'diag', 'zd']
            for field in diagnosis_fields:
                if hasattr(patient_data, field):
                    field_value = getattr(patient_data, field)
                    if field_value:
                        diag_codes.update(self._extract_diagnosis_from_field(field_value))

        except Exception as e:
            logger.warning(f"提取诊断代码时发生异常: {e}")

        return diag_codes

    def _extract_surgery_codes(self, patient_data: PatientData) -> set[str]:
        """
        从患者数据中提取手术代码

        Args:
            patient_data: 患者数据对象

        Returns:
            Set[str]: 手术代码集合
        """
        surgery_codes = set()

        try:
            # 手术相关字段名
            surgery_fields = [
                'surgery', 'surgeries', 'operation', 'operations',
                'surgical_procedure', 'procedures', 'ss', 'ssbm'  # 中文系统
            ]

            for field in surgery_fields:
                if hasattr(patient_data, field):
                    field_value = getattr(patient_data, field)
                    if field_value:
                        surgery_codes.update(self._extract_surgery_from_field(field_value))

            # 从费用明细中查找手术相关项目
            if hasattr(patient_data, 'fees') and patient_data.fees:
                for fee_item in patient_data.fees:
                    surgery_codes.update(self._extract_surgery_from_fee_item(fee_item))

        except Exception as e:
            logger.warning(f"提取手术代码时发生异常: {e}")

        return surgery_codes

    def _extract_codes_from_dict(self, data_dict: dict[str, Any], 
                                code_fields: list[str]) -> set[str]:
        """
        从字典中递归提取代码

        Args:
            data_dict: 数据字典
            code_fields: 代码字段名列表

        Returns:
            Set[str]: 提取到的代码集合
        """
        codes = set()

        try:
            for field in code_fields:
                if field in data_dict:
                    value = data_dict[field]
                    if value and isinstance(value, str) and value.strip():
                        codes.add(value.strip())

            # 递归处理嵌套字典
            for _, value in data_dict.items():
                if isinstance(value, dict):
                    codes.update(self._extract_codes_from_dict(value, code_fields))
                elif isinstance(value, list):
                    for item in value:
                        if isinstance(item, dict):
                            codes.update(self._extract_codes_from_dict(item, code_fields))

        except Exception as e:
            logger.debug(f"从字典提取代码时发生异常: {e}")

        return codes

    def _extract_diagnosis_from_field(self, field_value: Any) -> set[str]:
        """
        从诊断字段中提取代码

        Args:
            field_value: 诊断字段值

        Returns:
            Set[str]: 诊断代码集合
        """
        codes = set()

        try:
            if isinstance(field_value, str) and field_value.strip():
                # 直接字符串，可能是逗号分隔的代码
                for code in field_value.split(','):
                    if code.strip():
                        codes.add(code.strip())

            elif isinstance(field_value, list):
                for item in field_value:
                    if isinstance(item, str) and item.strip():
                        codes.add(item.strip())
                    elif isinstance(item, dict):
                        diag_fields = ['icd10', 'icd_10', 'code', 'zdm', 'bm']
                        codes.update(self._extract_codes_from_dict(item, diag_fields))

            elif isinstance(field_value, dict):
                diag_fields = ['icd10', 'icd_10', 'code', 'zdm', 'bm']
                codes.update(self._extract_codes_from_dict(field_value, diag_fields))

        except Exception as e:
            logger.debug(f"从诊断字段提取代码时发生异常: {e}")

        return codes

    def _extract_surgery_from_field(self, field_value: Any) -> set[str]:
        """
        从手术字段中提取代码

        Args:
            field_value: 手术字段值

        Returns:
            Set[str]: 手术代码集合
        """
        codes = set()

        try:
            if isinstance(field_value, str) and field_value.strip():
                # 直接字符串
                for code in field_value.split(','):
                    if code.strip():
                        codes.add(code.strip())

            elif isinstance(field_value, list):
                for item in field_value:
                    if isinstance(item, str) and item.strip():
                        codes.add(item.strip())
                    elif isinstance(item, dict):
                        surgery_fields = ['code', 'surgery_code', 'operation_code', 'ssbm']
                        codes.update(self._extract_codes_from_dict(item, surgery_fields))

            elif isinstance(field_value, dict):
                surgery_fields = ['code', 'surgery_code', 'operation_code', 'ssbm']
                codes.update(self._extract_codes_from_dict(field_value, surgery_fields))

        except Exception as e:
            logger.debug(f"从手术字段提取代码时发生异常: {e}")

        return codes

    def _extract_surgery_from_fee_item(self, fee_item: Any) -> set[str]:
        """
        从费用项目中提取手术代码

        Args:
            fee_item: 费用项目

        Returns:
            Set[str]: 手术代码集合
        """
        codes = set()

        try:
            # 检查费用项目类型或名称是否包含手术相关关键字
            surgery_keywords = ['手术', '术', 'surgery', 'operation', 'surgical']
            fee_type_fields = ['type', 'category', 'lx', 'lb', 'name', 'mc']

            is_surgery_related = False
            for field in fee_type_fields:
                field_value = None
                if hasattr(fee_item, field):
                    field_value = getattr(fee_item, field)
                elif isinstance(fee_item, dict) and field in fee_item:
                    field_value = fee_item[field]

                if field_value and isinstance(field_value, str):
                    for keyword in surgery_keywords:
                        if keyword in field_value:
                            is_surgery_related = True
                            break
                    if is_surgery_related:
                        break

            # 如果是手术相关费用，提取代码
            if is_surgery_related:
                code_fields = ['ybdm', 'yb_code', 'code', 'bm']
                for field in code_fields:
                    field_value = None
                    if hasattr(fee_item, field):
                        field_value = getattr(fee_item, field)
                    elif isinstance(fee_item, dict) and field in fee_item:
                        field_value = fee_item[field]

                    if field_value and isinstance(field_value, str) and field_value.strip():
                        codes.add(field_value.strip())

        except Exception as e:
            logger.debug(f"从费用项目提取手术代码时发生异常: {e}")

        return codes

    def get_performance_stats(self) -> dict[str, Any]:
        """获取性能统计信息"""
        avg_extraction_time = (
            self._total_extraction_time / self._extraction_count
            if self._extraction_count > 0 else 0.0
        )

        return {
            'extraction_count': self._extraction_count,
            'total_extraction_time_ms': round(self._total_extraction_time, 3),
            'avg_extraction_time_ms': round(avg_extraction_time, 3),
            'error_count': self._error_count,
            'error_rate': (
                self._error_count / self._extraction_count
                if self._extraction_count > 0 else 0.0
            )
        }

    def reset_stats(self) -> None:
        """重置统计信息"""
        self._extraction_count = 0
        self._total_extraction_time = 0.0
        self._error_count = 0


# 全局实例
patient_data_analyzer = PatientDataAnalyzer()
