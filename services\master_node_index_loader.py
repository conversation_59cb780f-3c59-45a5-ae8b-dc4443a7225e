"""
Task 2.1: 主节点索引加载器
实现主节点从数据库实时构建索引的核心功能

技术特点：
- 数据库查询性能优化：批量查询、索引利用、查询缓存
- 增量更新的变更检测：版本比较、时间戳检查、智能同步
- 大数据量的内存管理：流式处理、内存监控、垃圾回收
- 并发读写保护：读写锁、非阻塞设计、优雅降级
- 错误恢复机制：重试策略、回滚机制、状态恢复

验收标准：
- 能够在30秒内完成全量索引构建
- 增量更新响应时间小于5秒
- 支持并发读写，不阻塞校验服务
"""

import asyncio
import os
import time
from collections.abc import AsyncGenerator
from dataclasses import dataclass
from typing import Any

from sqlalchemy.orm import Session

from config.settings import settings
from core.async_rw_lock import AsyncRWLock
from core.logging.logging_system import log as logger
from core.memory_optimizer import MemoryOptimizer
from core.rule_index_manager import rule_index_manager
from core.smart_retry_mechanism import smart_retry
from models.database import RuleDetail, RuleDetailStatusEnum


@dataclass
class IndexBuildResult:
    """索引构建结果"""

    success: bool
    rule_count: int
    build_duration_ms: float
    memory_usage_mb: float
    memory_efficiency_ratio: float
    retry_count: int = 0
    lock_wait_time_ms: float = 0.0
    db_query_time_ms: float = 0.0
    error_message: str | None = None
    error_count: int = 0
    error_details: list[str] = None

    def __post_init__(self):
        if self.error_details is None:
            self.error_details = []


@dataclass
class IncrementalUpdateResult:
    """增量更新结果"""

    success: bool
    updated_count: int
    update_duration_ms: float
    retry_count: int = 0
    lock_wait_time_ms: float = 0.0
    error_message: str | None = None
    failed_rules: list[str] = None

    def __post_init__(self):
        if self.failed_rules is None:
            self.failed_rules = []


def get_rules_version() -> str:
    """获取规则版本信息"""
    # 这里可以从数据库或配置文件读取版本信息
    # 简化实现：使用文件时间戳作为版本
    try:
        if os.path.exists("rules_version.txt"):
            with open("rules_version.txt", "r", encoding="utf-8") as f:
                return f.read().strip()
        return "1.0.0"
    except Exception:
        return "1.0.0"


class MasterNodeIndexLoader:
    """
    主节点索引加载器

    负责从数据库实时构建和维护规则索引，提供高性能的规则预过滤功能。
    实现了完整的生命周期管理、性能优化和错误恢复机制。
    """

    def __init__(self):
        """初始化主节点索引加载器"""
        # 数据库会话
        self._db_session: Session | None = None

        # 同步状态管理
        self._build_in_progress = False
        self._rw_lock = AsyncRWLock(timeout_seconds=60.0)
        self._last_build_time = 0
        self._last_rules_version = ""

        # 后台同步配置
        self._background_sync_enabled = False
        self._sync_interval = int(os.getenv("INDEX_SYNC_INTERVAL", "300"))  # 5分钟
        self._sync_task: asyncio.Task | None = None

        # 性能和重试配置
        self._max_retry_count = int(os.getenv("INDEX_MAX_RETRIES", "3"))
        self._timeout_seconds = int(os.getenv("INDEX_TIMEOUT", "60"))
        self._batch_size = int(os.getenv("INDEX_BATCH_SIZE", "5000"))

        # 内存管理配置
        self._memory_limit_mb = int(os.getenv("INDEX_MEMORY_LIMIT_MB", "500"))
        self._gc_threshold = int(os.getenv("INDEX_GC_THRESHOLD", "1000"))

        # 集成智能内存管理器
        self.memory_optimizer = MemoryOptimizer(memory_limit_mb=self._memory_limit_mb)
        self.memory_optimizer.start_monitoring()
        logger.info(f"智能内存管理器已启动，限制: {self._memory_limit_mb}MB")

        # 配置验证
        validation_errors = self._validate_configuration()
        if validation_errors:
            error_msg = "配置验证失败: " + "; ".join(validation_errors)
            logger.error(error_msg)
            raise ValueError(error_msg)

        logger.info("主节点索引加载器初始化完成")

    def _validate_configuration(self) -> list[str]:
        """
        验证配置参数的合理性（增强版）

        Returns:
            list[str]: 验证错误列表，空列表表示验证通过
        """
        errors = []
        warnings = []

        # 批处理大小验证
        if self._batch_size <= 0:
            errors.append("批处理大小必须大于0")
        elif self._batch_size > 50000:
            errors.append("批处理大小不能超过50000，防止内存溢出")
        elif self._batch_size < 100:
            warnings.append("批处理大小过小，可能影响性能")

        # 内存限制验证
        if self._memory_limit_mb < 100:
            errors.append("内存限制不能小于100MB")
        elif self._memory_limit_mb > 10240:  # 10GB
            errors.append("内存限制不能超过10GB")
        elif self._memory_limit_mb < 200:
            warnings.append("内存限制较低，可能影响大数据量处理")

        # 超时时间验证
        if self._timeout_seconds <= 0:
            errors.append("超时时间必须大于0")
        elif self._timeout_seconds > 3600:  # 1小时
            errors.append("超时时间不能超过1小时")
        elif self._timeout_seconds < 30:
            warnings.append("超时时间过短，可能导致大数据量处理失败")

        # 同步间隔验证
        if self._sync_interval < 60:  # 1分钟
            errors.append("同步间隔不能小于60秒")
        elif self._sync_interval > 3600:  # 1小时
            warnings.append("同步间隔过长，可能导致数据不一致")

        # 重试次数验证
        if self._max_retry_count < 0 or self._max_retry_count > 10:
            errors.append("重试次数必须在0-10之间")

        # 垃圾回收阈值验证
        if self._gc_threshold < 100:
            warnings.append("垃圾回收阈值过低，可能影响性能")
        elif self._gc_threshold > 10000:
            warnings.append("垃圾回收阈值过高，可能导致内存压力")

        # 环境变量完整性检查
        required_env_vars = ["INDEX_SYNC_INTERVAL", "INDEX_MAX_RETRIES", "INDEX_TIMEOUT"]
        missing_vars = []
        for var in required_env_vars:
            if not os.getenv(var):
                missing_vars.append(var)

        if missing_vars:
            warnings.append(f"缺少环境变量配置: {', '.join(missing_vars)}，将使用默认值")

        # 配置一致性检查
        if self._batch_size * 2 > self._memory_limit_mb * 1024:  # 简化的内存检查
            warnings.append("批处理大小相对于内存限制过大，可能导致内存不足")

        # 输出警告信息
        if warnings:
            logger.warning(f"配置验证警告: {'; '.join(warnings)}")

        return errors

    async def build_full_index(self) -> IndexBuildResult:
        """
        构建全量索引

        验收标准：
        - 30秒内完成全量索引构建
        - 内存使用不超过规则数据的50%
        - 支持并发读写，不阻塞校验服务

        Returns:
            IndexBuildResult: 构建结果
        """
        # 并发保护 - 使用写锁
        if self._build_in_progress:
            raise RuntimeError("Index build already in progress")

        start_time = time.perf_counter()
        lock_start_time = time.perf_counter()

        try:
            # 获取写锁，保证构建过程的完整性
            async with self._rw_lock.write_lock():
                lock_wait_time = (time.perf_counter() - lock_start_time) * 1000
                self._build_in_progress = True

                logger.info("开始构建全量规则索引")

                # 检查数据库连接
                if not self._db_session:
                    return IndexBuildResult(
                        success=False,
                        rule_count=0,
                        build_duration_ms=0,
                        memory_usage_mb=0,
                        memory_efficiency_ratio=0,
                        lock_wait_time_ms=lock_wait_time,
                        error_message="数据库会话未初始化",
                    )

                # 使用重试机制执行数据库查询
                db_query_start = time.perf_counter()
                try:
                    rule_details = await smart_retry.execute_with_retry(
                        self._fetch_all_active_rules_optimized, config=smart_retry.create_db_retry_config()
                    )

                    if rule_details is None:
                        return IndexBuildResult(
                            success=False,
                            rule_count=0,
                            build_duration_ms=(time.perf_counter() - start_time) * 1000,
                            memory_usage_mb=0,
                            memory_efficiency_ratio=0,
                            lock_wait_time_ms=lock_wait_time,
                            error_message="数据库查询失败",
                        )

                    db_query_time = (time.perf_counter() - db_query_start) * 1000
                    retry_count = smart_retry.metrics.total_attempts - smart_retry.metrics.total_successes

                except Exception as e:
                    db_query_time = (time.perf_counter() - db_query_start) * 1000
                    retry_count = smart_retry.metrics.total_attempts - smart_retry.metrics.total_successes
                    error_msg = f"数据库查询重试失败: {e}"
                    logger.error(error_msg, exc_info=True)

                    return IndexBuildResult(
                        success=False,
                        rule_count=0,
                        build_duration_ms=(time.perf_counter() - start_time) * 1000,
                        memory_usage_mb=0,
                        memory_efficiency_ratio=0,
                        retry_count=retry_count,
                        lock_wait_time_ms=lock_wait_time,
                        db_query_time_ms=db_query_time,
                        error_message=error_msg,
                    )

                # 内存管理：分批处理大数据量
                total_rules = len(rule_details)
                logger.info(f"从数据库获取到 {total_rules} 条活跃规则")

                # 构建索引（非阻塞方式）
                build_result = await self._build_index_non_blocking(rule_details)

                # 更新状态
                self._last_build_time = time.time()
                self._last_rules_version = get_rules_version()

                # 智能内存优化：基于实际内存使用情况决定是否需要清理
                memory_stats = self.memory_optimizer.get_memory_stats()
                if memory_stats.is_over_threshold or total_rules > self._gc_threshold:
                    logger.info(f"索引构建完成后进行内存优化，当前使用: {memory_stats.process_memory_mb:.1f}MB")
                    self.memory_optimizer.optimize_memory(aggressive=False)

                build_duration = (time.perf_counter() - start_time) * 1000

                logger.info(
                    f"全量索引构建完成 - 耗时: {build_duration:.2f}ms, "
                    f"规则数量: {total_rules}, "
                    f"内存使用: {build_result.memory_usage_mb:.2f}MB, "
                    f"重试次数: {retry_count}, "
                    f"锁等待时间: {lock_wait_time:.2f}ms"
                )

                return IndexBuildResult(
                    success=build_result.success,
                    rule_count=total_rules,
                    build_duration_ms=build_duration,
                    memory_usage_mb=build_result.memory_usage_mb,
                    memory_efficiency_ratio=build_result.memory_efficiency_ratio,
                    retry_count=retry_count,
                    lock_wait_time_ms=lock_wait_time,
                    db_query_time_ms=db_query_time,
                    error_count=build_result.error_count,
                    error_details=build_result.error_details,
                )

        except Exception as e:
            build_duration = (time.perf_counter() - start_time) * 1000
            lock_wait_time = (time.perf_counter() - lock_start_time) * 1000
            error_msg = f"构建全量索引失败: {e}"
            logger.error(error_msg, exc_info=True)

            return IndexBuildResult(
                success=False,
                rule_count=0,
                build_duration_ms=build_duration,
                memory_usage_mb=0,
                memory_efficiency_ratio=0,
                lock_wait_time_ms=lock_wait_time,
                error_message=error_msg,
            )

        finally:
            self._build_in_progress = False

    async def incremental_update(self, updated_rules: list[RuleDetail]) -> IncrementalUpdateResult:
        """
        增量更新索引

        验收标准：
        - 增量更新响应时间小于5秒
        - 支持单个和批量更新
        - 更新失败时不影响现有索引

        Args:
            updated_rules: 需要更新的规则列表

        Returns:
            IncrementalUpdateResult: 更新结果
        """
        start_time = time.perf_counter()
        lock_start_time = time.perf_counter()

        try:
            logger.info(f"开始增量更新索引，规则数量: {len(updated_rules)}")

            # 使用读锁进行增量更新，减少对读操作的影响
            async with self._rw_lock.read_lock():
                lock_wait_time = (time.perf_counter() - lock_start_time) * 1000

                updated_count = 0
                failed_rules = []
                retry_count = 0

                # 执行批量更新（移除不必要的await和重试机制）
                try:
                    # 直接调用同步方法，不使用await
                    update_results = rule_index_manager.batch_update_rules(updated_rules)

                    for rule_id, success in update_results.items():
                        if success:
                            updated_count += 1
                        else:
                            failed_rules.append(rule_id)

                    retry_count = 0  # 简化重试计数

                except Exception as e:
                    retry_count = 0  # 简化重试计数，保持一致性
                    error_msg = f"增量更新失败: {e}"
                    logger.error(error_msg, exc_info=True)

                    update_duration = (time.perf_counter() - start_time) * 1000
                    return IncrementalUpdateResult(
                        success=False,
                        updated_count=0,
                        update_duration_ms=update_duration,
                        retry_count=retry_count,
                        lock_wait_time_ms=lock_wait_time,
                        error_message=error_msg,
                    )

                update_duration = (time.perf_counter() - start_time) * 1000

                # 更新时间戳
                self._last_build_time = time.time()

                logger.info(
                    f"增量更新完成 - 耗时: {update_duration:.2f}ms, "
                    f"成功: {updated_count}, 失败: {len(failed_rules)}, "
                    f"重试次数: {retry_count}, "
                    f"锁等待时间: {lock_wait_time:.2f}ms"
                )

                return IncrementalUpdateResult(
                    success=len(failed_rules) == 0,
                    updated_count=updated_count,
                    update_duration_ms=update_duration,
                    retry_count=retry_count,
                    lock_wait_time_ms=lock_wait_time,
                    failed_rules=failed_rules,
                )

        except Exception as e:
            update_duration = (time.perf_counter() - start_time) * 1000
            lock_wait_time = (time.perf_counter() - lock_start_time) * 1000
            error_msg = f"增量更新失败: {e}"
            logger.error(error_msg, exc_info=True)

            return IncrementalUpdateResult(
                success=False,
                updated_count=0,
                update_duration_ms=update_duration,
                lock_wait_time_ms=lock_wait_time,
                error_message=error_msg,
            )

    async def _fetch_all_active_rules_optimized(self) -> list[RuleDetail] | None:
        """
        优化的数据库查询：获取所有活跃规则

        性能优化：
        - 使用索引优化查询
        - 批量加载避免N+1问题
        - 只加载必要字段
        - 避免额外的COUNT查询
        """
        try:
            # 优化查询：使用索引、批量加载
            query = (
                self._db_session.query(RuleDetail)
                .filter(RuleDetail.status == RuleDetailStatusEnum.ACTIVE)
                .order_by(RuleDetail.id)  # 利用主键索引
            )

            # 改进：使用limit(1).first()来判断是否有数据，避免额外的COUNT查询
            first_record = query.limit(1).first()
            if not first_record:
                logger.info("没有找到活跃规则")
                return []

            # 如果数据量小，直接查询
            if self._batch_size <= 0:
                return query.all()

            # 大数据量分批处理
            all_rules = []
            offset = 0
            batch_count = 0

            # 增加最大批次限制，防止测试环境中的无限循环
            max_batches = int(os.getenv("INDEX_MAX_BATCHES", "1000"))  # 默认最多1000个批次

            while batch_count < max_batches:
                batch = query.offset(offset).limit(self._batch_size).all()
                if not batch:
                    break

                all_rules.extend(batch)
                offset += self._batch_size
                batch_count += 1

                # 智能内存压力检查：基于实际内存使用量触发优化
                memory_stats = self.memory_optimizer.get_memory_stats()
                if memory_stats.is_over_threshold:
                    logger.info(f"检测到内存压力 ({memory_stats.process_memory_mb:.1f}MB)，触发内存优化")
                    self.memory_optimizer.optimize_memory(aggressive=False)
                elif batch_count % 5 == 0:  # 每5个批次检查一次内存状态
                    logger.debug(f"内存使用状态: {memory_stats.process_memory_mb:.1f}MB / {memory_stats.threshold_mb:.1f}MB")

                # 防止无限循环：如果连续多次获取的数据量小于批处理大小，认为已到达末尾
                if len(batch) < self._batch_size:
                    break

                # 安全限制：防止内存耗尽（降低限制以适应测试环境）
                max_records = int(os.getenv("INDEX_MAX_RECORDS", "100000"))  # 默认10万条记录限制
                if len(all_rules) > max_records:
                    logger.warning(f"达到最大记录数限制({max_records})，停止加载，当前已加载: {len(all_rules)}")
                    break

            if batch_count >= max_batches:
                logger.warning(f"达到最大批次数限制({max_batches})，停止加载")

            logger.info(f"数据库查询完成，共加载 {len(all_rules)} 条记录，分 {batch_count} 个批次")
            return all_rules

        except Exception as e:
            logger.error(f"优化数据库查询失败: {e}", exc_info=True)
            return None

    async def _fetch_all_active_rules_streaming(self) -> AsyncGenerator[list[RuleDetail], None]:
        """
        流式获取活跃规则，减少内存峰值

        这个方法适用于非常大的数据集，可以按批次流式处理

        Yields:
            list[RuleDetail]: 每次返回一个批次的规则列表
        """
        try:
            query = (
                self._db_session.query(RuleDetail)
                .filter(RuleDetail.status == RuleDetailStatusEnum.ACTIVE)
                .order_by(RuleDetail.id)
            )

            offset = 0
            batch_count = 0

            while True:
                batch = query.offset(offset).limit(self._batch_size).all()
                if not batch:
                    break

                yield batch

                offset += self._batch_size
                batch_count += 1

                # 智能内存管理：基于实际内存压力触发优化
                memory_stats = self.memory_optimizer.get_memory_stats()
                if memory_stats.is_over_threshold:
                    logger.debug("流式处理中检测到内存压力，触发优化")
                    self.memory_optimizer.optimize_memory(aggressive=False)

                # 安全检查
                if len(batch) < self._batch_size:
                    break

                # 防止无限循环
                if batch_count > 10000:  # 最多1万个批次
                    logger.warning("达到最大批次数限制，停止流式处理")
                    break

            logger.debug(f"流式查询完成，共处理 {batch_count} 个批次")

        except Exception as e:
            logger.error(f"流式数据库查询失败: {e}", exc_info=True)
            raise

    async def _build_index_non_blocking(self, rule_details: list[RuleDetail]) -> IndexBuildResult:
        """
        非阻塞方式构建索引

        确保不阻塞校验服务的正常运行
        """
        try:
            start_time = time.perf_counter()

            # 在单独的线程中构建索引，避免阻塞
            loop = asyncio.get_event_loop()

            def build_sync():
                rule_index_manager.build_indexes_from_rule_details(rule_details)

                # 获取构建后的内存使用情况
                stats = rule_index_manager.get_performance_stats()
                metadata = stats.get("index_metadata", {})

                return {
                    "memory_usage_mb": metadata.get("memory_usage_mb", 0),
                    "rule_count": metadata.get("rule_count", 0)
                }

            # 在线程池中执行，避免阻塞事件循环
            result = await loop.run_in_executor(None, build_sync)

            build_duration = (time.perf_counter() - start_time) * 1000

            # 计算内存效率比（动态计算）
            memory_efficiency = self._calculate_memory_efficiency(
                result["memory_usage_mb"], 
                len(rule_details)
            )

            return IndexBuildResult(
                success=True,
                rule_count=result["rule_count"],
                build_duration_ms=build_duration,
                memory_usage_mb=result["memory_usage_mb"],
                memory_efficiency_ratio=memory_efficiency,
                error_count=0,
            )

        except Exception as e:
            logger.error(f"非阻塞索引构建失败: {e}", exc_info=True)
            return IndexBuildResult(
                success=False,
                rule_count=0,
                build_duration_ms=0,
                memory_usage_mb=0,
                memory_efficiency_ratio=0,
                error_message=str(e),
            )

    def _calculate_memory_efficiency(self, actual_memory_mb: float, rule_count: int) -> float:
        """
        动态计算内存效率比

        Args:
            actual_memory_mb: 实际内存使用量(MB)
            rule_count: 规则数量

        Returns:
            float: 内存效率比 (实际使用/理论使用)
        """
        if rule_count == 0:
            return 0.0

        # 基于规则复杂度的动态估算
        # 简单规则：约0.1KB，复杂规则：约2KB，平均约0.5KB
        base_size_per_rule_kb = 0.5

        # 索引开销估算：每个索引条目约占用50-100字节
        # 假设平均每个规则产生3个索引条目（医保代码、诊断代码、费用代码）
        index_overhead_per_rule_kb = 0.15  # 3 * 50 bytes = 150 bytes ≈ 0.15KB

        # 总理论内存使用
        estimated_total_kb = rule_count * (base_size_per_rule_kb + index_overhead_per_rule_kb)
        estimated_total_mb = estimated_total_kb / 1024

        # 计算效率比：实际使用/理论使用
        if estimated_total_mb > 0:
            efficiency_ratio = actual_memory_mb / estimated_total_mb
        else:
            efficiency_ratio = 0.0

        logger.debug(
            f"内存效率计算 - 规则数量: {rule_count}, "
            f"实际使用: {actual_memory_mb:.2f}MB, "
            f"理论使用: {estimated_total_mb:.2f}MB, "
            f"效率比: {efficiency_ratio:.3f}"
        )

        return efficiency_ratio

    def _detect_rule_changes(self) -> bool:
        """
        检测规则变更

        使用版本比较和时间戳检查实现智能同步
        """
        try:
            current_version = get_rules_version()
            return current_version != self._last_rules_version
        except Exception as e:
            logger.warning(f"检测规则变更失败: {e}")
            return True  # 出错时默认认为有变更

    async def _background_sync_loop(self):
        """后台同步循环"""
        logger.info(f"启动后台索引同步，间隔: {self._sync_interval}秒")

        while self._background_sync_enabled:
            try:
                await asyncio.sleep(self._sync_interval)

                if not self._background_sync_enabled:
                    break

                # 检查是否需要同步
                if self._detect_rule_changes():
                    logger.info("检测到规则变更，开始后台同步")
                    result = await self.build_full_index()

                    if result.success:
                        logger.info("后台索引同步完成")
                    else:
                        logger.error(f"后台索引同步失败: {result.error_message}")

            except asyncio.CancelledError:
                logger.info("后台同步循环被取消")
                break
            except Exception as e:
                logger.error(f"后台同步循环出错: {e}", exc_info=True)

    async def initialize_on_startup(self):
        """应用启动时初始化索引"""
        logger.info("应用启动，初始化索引加载器")

        try:
            # 构建初始索引
            result = await self.build_full_index()

            if result.success:
                logger.info("启动时索引构建成功")

                # 启动后台同步
                if settings.MODE == "master":  # 只在主节点启用后台同步
                    self._background_sync_enabled = True
                    self._sync_task = asyncio.create_task(self._background_sync_loop())

            else:
                logger.error(f"启动时索引构建失败: {result.error_message}")

        except Exception as e:
            logger.error(f"启动初始化失败: {e}", exc_info=True)

    def rebuild_index(self) -> IndexBuildResult:
        """重建索引接口（同步版本）"""
        logger.info("触发索引重建")

        # 使用异步接口
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)

        try:
            result = loop.run_until_complete(self.build_full_index())
            return result
        finally:
            loop.close()

    def set_database_session(self, session: Session):
        """设置数据库会话"""
        self._db_session = session
        logger.info("数据库会话已设置")

    def get_status(self) -> dict[str, Any]:
        """获取加载器状态，包含详细的性能指标"""
        # 获取锁状态
        lock_status = self._rw_lock.get_status()

        # 获取重试机制状态（如果可用）
        retry_status = {}
        try:
            if hasattr(smart_retry, "get_status"):
                retry_status = smart_retry.get_status()
        except:
            pass

        # 计算运行时长
        import time

        uptime_seconds = time.time() - self._last_build_time if self._last_build_time > 0 else 0

        return {
            # 基础状态
            "build_in_progress": self._build_in_progress,
            "last_build_time": self._last_build_time,
            "last_rules_version": self._last_rules_version,
            "background_sync_enabled": self._background_sync_enabled,
            "sync_interval": self._sync_interval,
            "memory_limit_mb": self._memory_limit_mb,
            "index_ready": rule_index_manager.is_ready(),
            "uptime_seconds": uptime_seconds,
            # 配置信息
            "configuration": {
                "batch_size": self._batch_size,
                "max_retry_count": self._max_retry_count,
                "timeout_seconds": self._timeout_seconds,
                "gc_threshold": self._gc_threshold,
            },
            # 锁性能指标
            "lock_metrics": lock_status,
            # 重试性能指标
            "retry_metrics": retry_status.get("metrics", {}),
            # 索引管理器状态
            "index_manager_status": rule_index_manager.get_performance_stats()
            if hasattr(rule_index_manager, "get_performance_stats")
            else {},
            # 系统资源信息
            "system_info": {
                "db_session_active": self._db_session is not None,
                "sync_task_active": self._sync_task is not None and not self._sync_task.done(),
            },
            # 内存优化器状态
            "memory_optimizer_status": {
                "is_monitoring": self.memory_optimizer.is_monitoring,
                "memory_limit_mb": self.memory_optimizer.memory_limit_mb,
                "warning_threshold_mb": self.memory_optimizer.warning_threshold,
                "critical_threshold_mb": self.memory_optimizer.critical_threshold,
                "cleanup_count": self.memory_optimizer.cleanup_count,
                "total_memory_freed_mb": self.memory_optimizer.total_memory_freed_mb,
                "last_cleanup_time": self.memory_optimizer.last_cleanup_time,
                "current_memory_stats": self.memory_optimizer.get_memory_stats().__dict__,
            },
            # 健康检查
            "health": {
                "status": "healthy" if rule_index_manager.is_ready() and not self._build_in_progress else "degraded",
                "last_successful_build": self._last_build_time > 0,
                "configuration_valid": len(self._validate_configuration()) == 0,
            },
        }

    async def shutdown(self):
        """优雅关闭"""
        logger.info("开始关闭索引加载器")

        # 停止内存优化器监控
        if hasattr(self, "memory_optimizer"):
            self.memory_optimizer.stop_monitoring()
            logger.info("内存优化器监控已停止")

        # 停止后台同步
        self._background_sync_enabled = False

        if self._sync_task and not self._sync_task.done():
            self._sync_task.cancel()
            try:
                await self._sync_task
            except asyncio.CancelledError:
                pass

        # 等待正在进行的构建完成
        max_wait = 30  # 最多等待30秒
        wait_time = 0

        while self._build_in_progress and wait_time < max_wait:
            await asyncio.sleep(1)
            wait_time += 1

        if self._build_in_progress:
            logger.warning("索引构建未能在30秒内完成，强制关闭")

        logger.info("索引加载器已关闭")


# 扩展索引管理器功能，添加验证方法
def validate_indexes() -> dict[str, Any]:
    """验证索引完整性"""
    try:
        if not rule_index_manager.is_ready():
            return {
                "valid": False,
                "total_indexes": 0,
                "errors": ["索引未就绪"]
            }

        stats = rule_index_manager.get_performance_stats()
        exact_indexes = stats.get("exact_index_sizes", {})

        total_indexes = sum(exact_indexes.values())
        errors = []

        # 基本验证
        if total_indexes == 0:
            errors.append("索引为空")

        # 检查各类型索引
        expected_types = ["yb_code", "diag_code", "surgery_code", "fee_code"]
        for index_type in expected_types:
            if index_type not in exact_indexes:
                errors.append(f"缺少{index_type}索引")

        return {
            "valid": len(errors) == 0,
            "total_indexes": total_indexes,
            "errors": errors,
            "index_details": exact_indexes
        }

    except Exception as e:
        return {
            "valid": False,
            "total_indexes": 0,
            "errors": [f"验证过程出错: {e}"]
        }

# 添加验证方法到索引管理器
rule_index_manager.validate_indexes = validate_indexes


# 全局实例
master_index_loader = MasterNodeIndexLoader()
