from functools import lru_cache
from typing import Literal

from pydantic_settings import BaseSettings, SettingsConfigDict


class Settings(BaseSettings):
    """
    Represents the application settings.
    Reads configuration from environment variables and/or a .env file.
    """

    # Model configuration
    model_config = SettingsConfigDict(
        env_file=".env",
        env_file_encoding="utf-8",
        case_sensitive=False,
        extra="ignore",
    )

    # --- Application Mode ---
    # Determines if the application runs as a 'master' or 'slave' node.
    MODE: Literal["master", "slave"] = "master"

    # --- Master Node Settings ---
    # These are only used when MOD<PERSON> is 'master'.

    # === 数据库配置方式1：传统URL方式（向后兼容） ===
    # Example: DATABASE_URL=mysql+pymysql://user:password@host:port/dbname
    DATABASE_URL: str | None = "mysql+pymysql://rule_user:mysql_password@***************:3306/rule_service"

    # === 数据库配置方式2：独立参数方式（推荐，解决特殊字符问题） ===
    # 数据库连接参数（优先级高于DATABASE_URL）
    DB_HOST: str | None = None
    DB_PORT: int = 3306
    DB_USER: str | None = None
    DB_PASSWORD: str | None = None
    DB_NAME: str | None = None
    DB_DRIVER: str = "pymysql"  # 数据库驱动：pymysql, mysqlclient

    # === 数据库自动创建配置 ===
    # 是否启用自动创建数据库功能（默认禁用，仅开发/测试环境建议启用）
    AUTO_CREATE_DATABASE: bool = False
    # 管理员账户配置（仅用于自动建库，需要CREATE权限）
    DB_ADMIN_USER: str | None = None
    DB_ADMIN_PASSWORD: str | None = None

    # === 数据库连接增强配置 ===
    # 数据库连接超时（秒）
    DB_CONNECT_TIMEOUT: int = 30
    # 是否在启动时检查数据库连接
    DB_CHECK_ON_STARTUP: bool = True

    # Secret key for signing tokens or securing APIs
    MASTER_API_SECRET_KEY: str | None = "a_very_secret_key_for_development"

    # --- Slave Node Settings ---
    # These are only used when MODE is 'slave'.
    MASTER_API_ENDPOINT: str | None = "http://localhost:18001"
    # 从节点的API Key必须与主节点的MASTER_API_SECRET_KEY匹配
    SLAVE_API_KEY: str | None = "a_very_secret_key_for_development"

    # HTTP/HTTPS proxy for the slave to connect through.
    # Example: http://user:<EMAIL>:8080
    PROXY_URL: str | None = None

    # --- Rule Synchronization Settings ---
    # 规则同步功能开关，仅适用于从节点（slave节点）
    # 主节点（master节点）强制启用同步功能，不受此配置影响
    ENABLE_RULE_SYNC: bool = True

    # 同步间隔（秒）
    RULE_SYNC_INTERVAL: int = 60

    # 同步超时（秒）
    RULE_SYNC_TIMEOUT: float = 120.0

    # 同步失败重试次数
    RULE_SYNC_MAX_RETRIES: int = 3

    # 同步失败重试间隔（秒）
    RULE_SYNC_RETRY_INTERVAL: float = 30.0

    # ===== 双模式同步配置 =====
    # 是否启用增量同步
    SYNC_INCREMENTAL_ENABLED: bool = True

    # 同步缓存大小（MB）
    SYNC_CACHE_SIZE_MB: int = 100

    # 同步包保留天数
    SYNC_PACKAGE_RETENTION_DAYS: int = 30

    # 同步压缩级别（1-9）
    SYNC_COMPRESSION_LEVEL: int = 6

    # 同步版本缓存TTL（秒）
    SYNC_VERSION_CACHE_TTL: int = 86400

    # 同步变更缓存TTL（秒）
    SYNC_CHANGES_CACHE_TTL: int = 7200

    # 同步结果缓存TTL（秒）
    SYNC_RESULT_CACHE_TTL: int = 1800

    # 同步状态检查间隔（秒）
    SYNC_STATUS_CHECK_INTERVAL: float = 5.0

    # 同步网络分区检测阈值
    SYNC_NETWORK_PARTITION_THRESHOLD: int = 3

    # 同步自动恢复开关
    SYNC_AUTO_RECOVERY_ENABLED: bool = True

    # ===== 离线包管理配置 =====
    # 离线包存储路径
    OFFLINE_PACKAGE_STORAGE_PATH: str = "data/offline_packages"

    # 离线包最大大小（MB）
    OFFLINE_PACKAGE_MAX_SIZE_MB: int = 100

    # 离线包清理间隔（秒）
    OFFLINE_PACKAGE_CLEANUP_INTERVAL: int = 3600

    # 离线包默认过期天数
    OFFLINE_PACKAGE_DEFAULT_EXPIRY_DAYS: int = 30

    # 离线包最大数量
    OFFLINE_PACKAGE_MAX_COUNT: int = 1000

    # 离线包默认压缩级别（1-9）
    OFFLINE_PACKAGE_DEFAULT_COMPRESSION_LEVEL: int = 6

    # 离线包生成超时（秒）
    OFFLINE_PACKAGE_GENERATION_TIMEOUT: float = 300.0

    # 离线包下载超时（秒）
    OFFLINE_PACKAGE_DOWNLOAD_TIMEOUT: float = 120.0

    # 离线包校验开关
    OFFLINE_PACKAGE_INTEGRITY_CHECK_ENABLED: bool = True

    # ===== 同步缓存配置 =====
    # 版本缓存配置
    CACHE_VERSION_MAX_SIZE: int = 10000
    CACHE_VERSION_MAX_MEMORY_MB: int = 20
    CACHE_VERSION_TTL_SECONDS: int = 86400
    CACHE_VERSION_CLEANUP_INTERVAL: int = 3600

    # 变更缓存配置
    CACHE_CHANGES_MAX_SIZE: int = 1000
    CACHE_CHANGES_MAX_MEMORY_MB: int = 100
    CACHE_CHANGES_TTL_SECONDS: int = 7200
    CACHE_CHANGES_CLEANUP_INTERVAL: int = 600

    # 包缓存配置
    CACHE_PACKAGE_MAX_SIZE: int = 500
    CACHE_PACKAGE_MAX_MEMORY_MB: int = 50
    CACHE_PACKAGE_TTL_SECONDS: int = 3600
    CACHE_PACKAGE_CLEANUP_INTERVAL: int = 300

    # 同步结果缓存配置
    CACHE_SYNC_RESULT_MAX_SIZE: int = 1000
    CACHE_SYNC_RESULT_MAX_MEMORY_MB: int = 30
    CACHE_SYNC_RESULT_TTL_SECONDS: int = 1800
    CACHE_SYNC_RESULT_CLEANUP_INTERVAL: int = 180

    # 缓存性能监控开关
    CACHE_PERFORMANCE_MONITORING_ENABLED: bool = True

    # 缓存统计收集间隔（秒）
    CACHE_STATS_COLLECTION_INTERVAL: float = 60.0

    # --- Shared Settings ---
    # Environment Mode: DEV, TEST, PROD
    RUN_MODE: str = "PROD"

    # Logging Configuration
    # 基础日志配置（向后兼容）
    LOG_LEVEL: str = "INFO"
    LOG_PATH: str = "logs/app_{time}.log"
    LOG_FORMAT: str = "{time:YYYY-MM-DD HH:mm:ss.SSS} | {level: <5} | {name}:{function}:{line} - {message}"
    LOG_ROTATION: str = "10 MB"
    LOG_RETENTION: str = "7 days"
    LOG_COMPRESSION: str = "zip"
    LOG_SERIALIZE: bool = False  # Set to True for JSON logs
    LOG_ENQUEUE: bool = True
    LOG_STDOUT_SINK_ENABLED: bool = True

    # 环境特定日志配置
    # 是否启用环境特定的日志配置（默认启用）
    LOG_USE_ENVIRONMENT_CONFIG: bool = True

    # 强制指定日志环境（可选，如果不设置则自动检测）
    LOG_FORCE_ENVIRONMENT: str | None = None

    # 敏感信息过滤配置
    LOG_FILTER_SENSITIVE_DATA: bool = True

    # 环境特定配置覆盖
    # 开发环境配置
    LOG_DEV_LEVEL: str = "DEBUG"
    LOG_DEV_STDOUT_ENABLED: bool = True
    LOG_DEV_ROTATION: str = "5 MB"
    LOG_DEV_RETENTION: str = "3 days"

    # 测试环境配置
    LOG_TEST_LEVEL: str = "INFO"
    LOG_TEST_STDOUT_ENABLED: bool = False
    LOG_TEST_ROTATION: str = "10 MB"
    LOG_TEST_RETENTION: str = "7 days"

    # 生产环境配置
    LOG_PROD_LEVEL: str = "WARNING"
    LOG_PROD_STDOUT_ENABLED: bool = False
    LOG_PROD_ROTATION: str = "50 MB"
    LOG_PROD_RETENTION: str = "30 days"
    LOG_PROD_COMPRESSION: str = "gz"

    # Web Server Configuration
    SERVER_HOST: str = "0.0.0.0"
    SERVER_PORT: int = 18001
    SERVER_RELOAD: bool = False

    # Worker Configuration
    WORKER_COUNT: int = 16
    QUEUE_MAX_SIZE: int = 1000
    REQUEST_TIMEOUT: float = 60.0

    # Rule Service Configuration
    RULE_SERVICE_MAX_WORKERS: int = 10
    RULE_SERVICE_MAX_TASKS_PER_WORKER: int = 100

    # Ultra-Fast Rule Validation Configuration
    # 是否启用超快速校验引擎（默认启用）
    ENABLE_ULTRA_FAST_VALIDATION: bool = True
    # 是否启用超级优化（数据预处理+规则筛选）
    ENABLE_ULTRA_OPTIMIZATION: bool = True
    # 规则筛选目标减少百分比（0-100，表示期望减少的规则数量百分比）
    RULE_FILTER_TARGET_REDUCTION: float = 70.0
    # 内存使用限制（MB）
    ULTRA_FAST_MEMORY_LIMIT_MB: int = 1024
    # 是否启用性能监控和统计
    ENABLE_PERFORMANCE_MONITORING: bool = True

    # Rule Prefilter Configuration - Task 2.1
    # 是否启用规则预过滤功能（默认关闭，确保平滑升级）
    ENABLE_RULE_PREFILTER: bool = False
    # 前缀匹配算法选择：trie|mapping|auto
    PREFILTER_ALGORITHM: str = "auto"
    # 过滤超时时间（毫秒）
    PREFILTER_TIMEOUT_MS: int = 10
    # 降级触发阈值（失败率）
    PREFILTER_FALLBACK_THRESHOLD: float = 0.1
    # 索引同步间隔（秒）
    INDEX_SYNC_INTERVAL: int = 300
    # 索引构建最大重试次数
    INDEX_MAX_RETRIES: int = 3
    # 索引构建超时时间（秒）
    INDEX_TIMEOUT: int = 60
    # 索引内存限制（MB）
    INDEX_MEMORY_LIMIT_MB: int = 500

    # Business Logic Configuration
    FEE_SELF_PAY_CODE: list[str] = ["400", "300", "0", "null"]

    # Database Connection Pool Configuration
    DB_POOL_SIZE: int = 20
    DB_MAX_OVERFLOW: int = 30
    DB_POOL_TIMEOUT: int = 30
    DB_POOL_RECYCLE: int = 3600
    DB_POOL_PRE_PING: bool = True
    DB_POOL_RESET_ON_RETURN: str = "commit"

    # Dynamic Pool Adjustment Configuration
    DB_POOL_MIN_SIZE: int = 10
    DB_POOL_MAX_SIZE: int = 50
    DB_POOL_ADJUSTMENT_INTERVAL: int = 60
    DB_POOL_AUTO_ADJUST: bool = True

    # Connection Pool Monitoring Configuration
    DB_POOL_MONITOR_ENABLED: bool = True
    DB_POOL_LEAK_DETECTION_ENABLED: bool = True
    DB_POOL_LEAK_THRESHOLD: float = 0.9
    DB_POOL_WARMUP_CONNECTIONS: int = 10

    # ===== HTTP重试机制配置 =====
    # HTTP重试功能总开关
    HTTP_RETRY_ENABLED: bool = True

    # 基础重试参数
    HTTP_RETRY_MAX_ATTEMPTS: int = 3
    HTTP_RETRY_BASE_DELAY: float = 1.0
    HTTP_RETRY_MAX_DELAY: float = 60.0
    HTTP_RETRY_BACKOFF_FACTOR: float = 2.0
    HTTP_RETRY_JITTER: bool = True

    # 可重试状态码配置（逗号分隔的字符串，支持环境变量）
    HTTP_RETRY_STATUS_CODES: str = "408,429,500,502,503,504"

    # 可重试异常类型配置
    HTTP_RETRY_ON_CONNECTION_ERROR: bool = True
    HTTP_RETRY_ON_TIMEOUT_ERROR: bool = True
    HTTP_RETRY_ON_DNS_ERROR: bool = True

    # ===== 断路器配置 =====
    # 断路器功能开关
    CIRCUIT_BREAKER_ENABLED: bool = True

    # 断路器基础参数
    CIRCUIT_BREAKER_FAILURE_THRESHOLD: int = 5  # 失败阈值(次数)
    CIRCUIT_BREAKER_FAILURE_RATE_THRESHOLD: float = 0.5  # 失败率阈值(0.0-1.0)
    CIRCUIT_BREAKER_RECOVERY_TIMEOUT: float = 60.0  # 恢复超时(秒)
    CIRCUIT_BREAKER_WINDOW_SIZE: int = 100  # 滑动窗口大小
    CIRCUIT_BREAKER_HALF_OPEN_MAX_CALLS: int = 3  # 半开状态最大调用数

    # ===== 降级机制配置 =====
    # 降级功能总开关
    DEGRADATION_ENABLED: bool = True

    # 降级同步配置（slave模式）
    DEGRADATION_SYNC_INTERVAL: float = 30.0  # 同步间隔（秒）
    DEGRADATION_SYNC_TIMEOUT: float = 10.0  # 同步超时（秒）
    DEGRADATION_SYNC_MAX_FAILURES: int = 5  # 最大失败次数
    DEGRADATION_STATE_CACHE_TTL: float = 600.0  # 状态缓存TTL（秒）

    # 网络分区处理配置
    DEGRADATION_NETWORK_PARTITION_THRESHOLD: int = 3  # 网络分区阈值
    DEGRADATION_AUTO_SAFETY_MODE: bool = True  # 网络分区时自动安全模式

    # ===== 环境特定重试配置 =====
    # 开发环境：更激进的重试策略，便于调试
    HTTP_RETRY_DEV_ENABLED: bool = True
    HTTP_RETRY_DEV_MAX_ATTEMPTS: int = 5
    HTTP_RETRY_DEV_BASE_DELAY: float = 0.5
    HTTP_RETRY_DEV_MAX_DELAY: float = 30.0
    HTTP_RETRY_DEV_BACKOFF_FACTOR: float = 1.5

    # 测试环境：快速失败，减少测试时间
    HTTP_RETRY_TEST_ENABLED: bool = True
    HTTP_RETRY_TEST_MAX_ATTEMPTS: int = 2
    HTTP_RETRY_TEST_BASE_DELAY: float = 0.1
    HTTP_RETRY_TEST_MAX_DELAY: float = 5.0
    HTTP_RETRY_TEST_BACKOFF_FACTOR: float = 2.0

    # 生产环境：保守的重试策略，确保稳定性
    HTTP_RETRY_PROD_ENABLED: bool = True
    HTTP_RETRY_PROD_MAX_ATTEMPTS: int = 3
    HTTP_RETRY_PROD_BASE_DELAY: float = 2.0
    HTTP_RETRY_PROD_MAX_DELAY: float = 120.0
    HTTP_RETRY_PROD_BACKOFF_FACTOR: float = 2.0

    # ===== 重试监控配置 =====
    # 重试指标收集开关
    HTTP_RETRY_METRICS_ENABLED: bool = True

    # 重试指标保留时间(秒)
    HTTP_RETRY_METRICS_RETENTION: int = 3600  # 1小时

    # 重试统计窗口大小
    HTTP_RETRY_STATS_WINDOW_SIZE: int = 1000

    # ===== 性能降级机制配置 =====
    # 降级功能总开关
    DEGRADATION_ENABLED: bool = True

    # ===== 基础降级触发阈值配置 =====
    # CPU使用率触发阈值（百分比）
    DEGRADATION_CPU_L1_THRESHOLD: float = 75.0  # L1轻度降级CPU阈值
    DEGRADATION_CPU_L2_THRESHOLD: float = 85.0  # L2中度降级CPU阈值
    DEGRADATION_CPU_L3_THRESHOLD: float = 95.0  # L3重度降级CPU阈值

    # 内存使用率触发阈值（百分比）
    DEGRADATION_MEMORY_L1_THRESHOLD: float = 80.0  # L1轻度降级内存阈值
    DEGRADATION_MEMORY_L2_THRESHOLD: float = 90.0  # L2中度降级内存阈值
    DEGRADATION_MEMORY_L3_THRESHOLD: float = 95.0  # L3重度降级内存阈值

    # 错误率触发阈值（百分比）
    DEGRADATION_ERROR_L1_THRESHOLD: float = 5.0  # L1轻度降级错误率阈值
    DEGRADATION_ERROR_L2_THRESHOLD: float = 10.0  # L2中度降级错误率阈值
    DEGRADATION_ERROR_L3_THRESHOLD: float = 20.0  # L3重度降级错误率阈值

    # 队列长度触发阈值
    DEGRADATION_QUEUE_L1_THRESHOLD: int = 100  # L1轻度降级队列长度阈值
    DEGRADATION_QUEUE_L2_THRESHOLD: int = 200  # L2中度降级队列长度阈值
    DEGRADATION_QUEUE_L3_THRESHOLD: int = 500  # L3重度降级队列长度阈值

    # ===== 降级恢复阈值配置（支持滞后恢复） =====
    # CPU使用率恢复阈值（百分比）
    DEGRADATION_CPU_L1_RECOVERY: float = 65.0  # L1恢复CPU阈值
    DEGRADATION_CPU_L2_RECOVERY: float = 75.0  # L2恢复CPU阈值
    DEGRADATION_CPU_L3_RECOVERY: float = 85.0  # L3恢复CPU阈值

    # 内存使用率恢复阈值（百分比）
    DEGRADATION_MEMORY_L1_RECOVERY: float = 70.0  # L1恢复内存阈值
    DEGRADATION_MEMORY_L2_RECOVERY: float = 80.0  # L2恢复内存阈值
    DEGRADATION_MEMORY_L3_RECOVERY: float = 90.0  # L3恢复内存阈值

    # 错误率恢复阈值（百分比）
    DEGRADATION_ERROR_L1_RECOVERY: float = 2.0  # L1恢复错误率阈值
    DEGRADATION_ERROR_L2_RECOVERY: float = 5.0  # L2恢复错误率阈值
    DEGRADATION_ERROR_L3_RECOVERY: float = 10.0  # L3恢复错误率阈值

    # 队列长度恢复阈值
    DEGRADATION_QUEUE_L1_RECOVERY: int = 50  # L1恢复队列长度阈值
    DEGRADATION_QUEUE_L2_RECOVERY: int = 100  # L2恢复队列长度阈值
    DEGRADATION_QUEUE_L3_RECOVERY: int = 200  # L3恢复队列长度阈值

    # ===== 降级检测配置 =====
    # 滑动窗口大小（样本数）
    DEGRADATION_WINDOW_SIZE: int = 10  # 默认滑动窗口大小
    DEGRADATION_MIN_SAMPLES: int = 3  # 最小样本数要求
    DEGRADATION_CHECK_INTERVAL: float = 5.0  # 检测间隔（秒）

    # 降级状态保持时间（秒）
    DEGRADATION_MIN_DURATION: float = 30.0  # 最小降级持续时间
    DEGRADATION_RECOVERY_DELAY: float = 60.0  # 恢复检测延迟时间

    # ===== 环境特定降级配置 =====
    # 开发环境：更敏感的降级策略，便于测试和调试
    DEGRADATION_DEV_ENABLED: bool = True
    DEGRADATION_DEV_CPU_L1_THRESHOLD: float = 60.0
    DEGRADATION_DEV_CPU_L2_THRESHOLD: float = 70.0
    DEGRADATION_DEV_CPU_L3_THRESHOLD: float = 80.0
    DEGRADATION_DEV_MEMORY_L1_THRESHOLD: float = 70.0
    DEGRADATION_DEV_MEMORY_L2_THRESHOLD: float = 80.0
    DEGRADATION_DEV_MEMORY_L3_THRESHOLD: float = 90.0
    DEGRADATION_DEV_CHECK_INTERVAL: float = 2.0
    DEGRADATION_DEV_MIN_DURATION: float = 10.0

    # 测试环境：快速降级和恢复，减少测试时间
    DEGRADATION_TEST_ENABLED: bool = True
    DEGRADATION_TEST_CPU_L1_THRESHOLD: float = 70.0
    DEGRADATION_TEST_CPU_L2_THRESHOLD: float = 80.0
    DEGRADATION_TEST_CPU_L3_THRESHOLD: float = 90.0
    DEGRADATION_TEST_MEMORY_L1_THRESHOLD: float = 75.0
    DEGRADATION_TEST_MEMORY_L2_THRESHOLD: float = 85.0
    DEGRADATION_TEST_MEMORY_L3_THRESHOLD: float = 95.0
    DEGRADATION_TEST_CHECK_INTERVAL: float = 1.0
    DEGRADATION_TEST_MIN_DURATION: float = 5.0
    DEGRADATION_TEST_RECOVERY_DELAY: float = 10.0

    # 生产环境：保守的降级策略，确保稳定性
    DEGRADATION_PROD_ENABLED: bool = True
    DEGRADATION_PROD_CPU_L1_THRESHOLD: float = 80.0
    DEGRADATION_PROD_CPU_L2_THRESHOLD: float = 90.0
    DEGRADATION_PROD_CPU_L3_THRESHOLD: float = 98.0
    DEGRADATION_PROD_MEMORY_L1_THRESHOLD: float = 85.0
    DEGRADATION_PROD_MEMORY_L2_THRESHOLD: float = 95.0
    DEGRADATION_PROD_MEMORY_L3_THRESHOLD: float = 98.0
    DEGRADATION_PROD_CHECK_INTERVAL: float = 10.0
    DEGRADATION_PROD_MIN_DURATION: float = 60.0
    DEGRADATION_PROD_RECOVERY_DELAY: float = 120.0

    # ===== 降级监控配置 =====
    # 降级指标收集开关
    DEGRADATION_METRICS_ENABLED: bool = True

    # 降级指标保留时间(秒)
    DEGRADATION_METRICS_RETENTION: int = 7200  # 2小时

    # 降级事件历史保留数量
    DEGRADATION_EVENT_HISTORY_SIZE: int = 1000

    # 降级统计窗口大小
    DEGRADATION_STATS_WINDOW_SIZE: int = 500

    # 降级状态同步间隔（master-slave架构）
    DEGRADATION_SYNC_INTERVAL: float = 30.0

    # 降级状态持久化开关
    DEGRADATION_PERSISTENCE_ENABLED: bool = True

    # ===== 规则注册服务配置 =====
    # 规则注册功能总开关
    RULE_REGISTRATION_ENABLED: bool = True

    # 注册服务地址配置
    RULE_REGISTRATION_HOST: str = "http://localhost:6060"

    # 注册服务超时配置（秒）
    RULE_REGISTRATION_TIMEOUT: float = 30.0

    # 注册服务重试配置
    RULE_REGISTRATION_MAX_RETRIES: int = 3

    # 注册服务分批处理配置
    RULE_REGISTRATION_BATCH_SIZE: int = 100

    # 注册任务队列配置
    RULE_REGISTRATION_QUEUE_MAX_SIZE: int = 1000

    # 注册任务超时配置（秒）
    RULE_REGISTRATION_TASK_TIMEOUT: float = 300.0

    # 注册Worker数量配置
    REGISTRATION_WORKER_COUNT: int = 2

    # ===== 注册任务性能监控配置 =====
    # 注册任务性能监控功能开关
    RULE_REGISTRATION_PERFORMANCE_MONITORING_ENABLED: bool = True

    # 注册任务性能指标历史记录大小
    RULE_REGISTRATION_METRICS_HISTORY_SIZE: int = 100

    # 注册任务性能阈值配置
    RULE_REGISTRATION_PERFORMANCE_CPU_THRESHOLD: float = 80.0  # CPU使用率阈值
    RULE_REGISTRATION_PERFORMANCE_MEMORY_THRESHOLD: float = 85.0  # 内存使用率阈值
    RULE_REGISTRATION_PERFORMANCE_SUCCESS_RATE_THRESHOLD: float = 0.95  # 成功率阈值
    RULE_REGISTRATION_PERFORMANCE_AVG_TIME_THRESHOLD: float = 5.0  # 平均处理时间阈值（秒）

    # 注册任务批次大小优化阈值
    RULE_REGISTRATION_BATCH_SIZE_MIN: int = 50  # 最小批次大小
    RULE_REGISTRATION_BATCH_SIZE_MAX: int = 500  # 最大批次大小
    RULE_REGISTRATION_BATCH_ADJUSTMENT_FACTOR: float = 0.2  # 批次大小调整因子

    # ===== 自适应批次大小管理器配置 =====
    # 自适应批次大小管理功能开关
    ADAPTIVE_BATCH_SIZE_ENABLED: bool = True

    # 批次大小调整间隔（秒）
    ADAPTIVE_BATCH_ADJUSTMENT_INTERVAL: float = 30.0

    # 批次大小调整阈值
    ADAPTIVE_BATCH_SCALE_UP_CPU_THRESHOLD: float = 50.0  # CPU使用率低于此值时考虑增大批次
    ADAPTIVE_BATCH_SCALE_DOWN_CPU_THRESHOLD: float = 85.0  # CPU使用率高于此值时考虑减小批次
    ADAPTIVE_BATCH_SCALE_UP_MEMORY_THRESHOLD: float = 70.0  # 内存使用率低于此值时考虑增大批次
    ADAPTIVE_BATCH_SCALE_DOWN_MEMORY_THRESHOLD: float = 85.0  # 内存使用率高于此值时考虑减小批次

    # 批次大小调整步长
    ADAPTIVE_BATCH_ADJUSTMENT_STEP: float = 0.2  # 每次调整的比例（20%）
    ADAPTIVE_BATCH_MIN_ADJUSTMENT: int = 10  # 最小调整量
    ADAPTIVE_BATCH_MAX_ADJUSTMENT: int = 100  # 最大调整量

    # 性能指标权重配置
    ADAPTIVE_BATCH_CPU_WEIGHT: float = 0.4  # CPU使用率权重
    ADAPTIVE_BATCH_MEMORY_WEIGHT: float = 0.3  # 内存使用率权重
    ADAPTIVE_BATCH_SUCCESS_RATE_WEIGHT: float = 0.2  # 成功率权重
    ADAPTIVE_BATCH_PROCESSING_TIME_WEIGHT: float = 0.1  # 处理时间权重

    # 调整历史记录大小
    ADAPTIVE_BATCH_ADJUSTMENT_HISTORY_SIZE: int = 50

    # ===== 智能分批处理集成配置 =====
    # 智能分批处理功能开关（主开关）
    RULE_REGISTRATION_ADAPTIVE_BATCH_ENABLED: bool = False  # 默认禁用，确保向后兼容

    # A/B测试配置
    RULE_REGISTRATION_AB_TEST_ENABLED: bool = False  # A/B测试开关
    RULE_REGISTRATION_AB_TEST_RATIO: float = 0.5  # A/B测试比例（0.0-1.0）

    # 智能分批处理模式配置
    RULE_REGISTRATION_STREAMING_MODE_ENABLED: bool = True  # 流式处理模式开关
    RULE_REGISTRATION_MAX_RETRIES: int = 3  # 最大重试次数

    # 性能提升目标配置
    RULE_REGISTRATION_PERFORMANCE_TARGET: float = 0.25  # 性能提升目标（25%）
    RULE_REGISTRATION_PERFORMANCE_BASELINE_ENABLED: bool = True  # 性能基线记录开关

    # ===== 服务降级策略配置 =====
    # 服务降级功能开关
    RULE_REGISTRATION_DEGRADED_MODE_ENABLED: bool = True

    # 健康检查配置
    RULE_REGISTRATION_HEALTH_CHECK_INTERVAL: float = 30.0  # 健康检查间隔（秒）
    RULE_REGISTRATION_HEALTH_CHECK_TIMEOUT: float = 5.0  # 健康检查超时（秒）

    # 熔断器配置
    RULE_REGISTRATION_CIRCUIT_BREAKER_FAILURE_THRESHOLD: float = 0.5  # 失败率阈值
    RULE_REGISTRATION_CIRCUIT_BREAKER_MIN_REQUESTS: int = 10  # 最小请求数
    RULE_REGISTRATION_CIRCUIT_BREAKER_TIMEOUT: float = 60.0  # 熔断超时时间（秒）
    RULE_REGISTRATION_CIRCUIT_BREAKER_HALF_OPEN_MAX_CALLS: int = 3  # 半开状态最大调用次数

    # 缓存配置
    RULE_REGISTRATION_CACHE_MAX_SIZE: int = 10000  # 缓存最大条目数
    RULE_REGISTRATION_CACHE_TTL: float = 3600.0  # 缓存TTL（秒）

    # 同步配置
    RULE_REGISTRATION_SYNC_BATCH_SIZE: int = 100  # 同步批次大小
    RULE_REGISTRATION_SYNC_INTERVAL: float = 300.0  # 同步间隔（秒）

    # ===== 任务状态管理配置 =====
    # 任务状态存储类型 (memory | database)
    TASK_STATUS_STORAGE_TYPE: str = "memory"

    # 数据库存储相关配置
    TASK_STATUS_DB_CLEANUP_INTERVAL: int = 3600  # 1小时清理间隔
    TASK_STATUS_DB_RETENTION_DAYS: int = 7  # 7天保留期

    # 任务恢复配置
    TASK_RECOVERY_ENABLED: bool = True  # 是否启用任务恢复
    TASK_RECOVERY_ON_STARTUP: bool = True  # 启动时是否自动恢复任务

    # ===== 结构化存储配置 =====
    # 启用结构化存储
    ENABLE_STRUCTURED_STORAGE: bool = True
    # 是否启用JSON备份
    ENABLE_JSON_BACKUP: bool = False

    # ===== 启动优化配置 =====
    # 启用启动优化
    ENABLE_STARTUP_OPTIMIZATION: bool = True
    # 启动优化总超时时间（秒）
    STARTUP_OPTIMIZATION_TIMEOUT: int = 600  # 10分钟
    # 启用并行预热
    ENABLE_PARALLEL_WARMUP: bool = True
    # 超时时跳过预热
    SKIP_WARMUP_ON_TIMEOUT: bool = True

    # ===== 缓存预热配置 =====
    # 启用启动预热
    ENABLE_STARTUP_WARMUP: bool = True
    # 预热超时时间（秒）
    WARMUP_TIMEOUT_SECONDS: int = 300  # 5分钟
    # 预热内存限制（MB）
    WARMUP_MEMORY_LIMIT_MB: int = 200
    # 预热批次大小
    WARMUP_BATCH_SIZE: int = 5
    # 预热最大并发数
    WARMUP_MAX_CONCURRENT: int = 3

    # ===== 索引预构建配置 =====
    # 启用索引预构建
    ENABLE_INDEX_PREBUILD: bool = True
    # 索引构建超时时间（秒）
    INDEX_BUILD_TIMEOUT_SECONDS: int = 120  # 2分钟
    # 索引构建内存限制（MB）
    INDEX_BUILD_MEMORY_LIMIT_MB: int = 100

    # ===== 超快速校验配置 =====
    # 启用超快速校验引擎
    ENABLE_ULTRA_FAST_VALIDATION: bool = True
    # 启用超级优化（数据预处理+规则筛选）
    ENABLE_ULTRA_OPTIMIZATION: bool = True
    # 规则筛选目标减少百分比
    RULE_FILTER_TARGET_REDUCTION: float = 70.0
    # 超快速校验内存限制（MB）
    ULTRA_FAST_MEMORY_LIMIT_MB: int = 1024
    # 启用性能监控
    ENABLE_PERFORMANCE_MONITORING: bool = True

    # ===== 规则预过滤配置 =====
    # 规则预过滤功能总开关
    ENABLE_RULE_PREFILTER: bool = False

    # 预过滤算法选择 (auto | trie | mapping)
    PREFILTER_ALGORITHM: str = "auto"

    # 预过滤超时时间（毫秒）
    PREFILTER_TIMEOUT_MS: float = 10.0

    # 预过滤降级阈值（降级率超过此值时认为不健康）
    PREFILTER_FALLBACK_THRESHOLD: float = 0.1

    # 预过滤器自动恢复开关
    PREFILTER_AUTO_RECOVERY_ENABLED: bool = True

    # 预过滤索引构建超时（秒）
    PREFILTER_INDEX_BUILD_TIMEOUT: int = 30

    # 预过滤索引内存限制（MB）
    PREFILTER_INDEX_MEMORY_LIMIT_MB: int = 50

    # 前缀匹配阈值（当前缀数量超过此值时使用Trie树）
    PREFILTER_PREFIX_THRESHOLD: int = 1000

    # Add other configuration variables here
    # For example:
    # API_KEY: str
    # WORKER_CONCURRENCY: int = 4

    def get_database_url(self) -> str | None:
        """
        获取数据库连接URL
        优先使用独立参数构建，如果没有则使用DATABASE_URL

        Returns:
            str | None: 数据库连接URL，如果配置不完整则返回None
        """
        # 优先使用独立参数方式
        if all([self.DB_HOST, self.DB_USER, self.DB_PASSWORD, self.DB_NAME]):
            # 构建数据库URL，无需URL编码，直接使用原始密码
            return f"mysql+{self.DB_DRIVER}://{self.DB_USER}:{self.DB_PASSWORD}@{self.DB_HOST}:{self.DB_PORT}/{self.DB_NAME}"

        # 回退到传统DATABASE_URL方式
        return self.DATABASE_URL

    def get_admin_database_url(self) -> str | None:
        """
        获取管理员数据库连接URL（用于自动建库）
        连接到MySQL服务器但不指定数据库

        Returns:
            str | None: 管理员数据库连接URL，如果配置不完整则返回None
        """
        if not self.AUTO_CREATE_DATABASE:
            return None

        # 优先使用独立参数 + 管理员账户
        if all([self.DB_HOST, self.DB_ADMIN_USER, self.DB_ADMIN_PASSWORD]):
            return f"mysql+{self.DB_DRIVER}://{self.DB_ADMIN_USER}:{self.DB_ADMIN_PASSWORD}@{self.DB_HOST}:{self.DB_PORT}"

        # 如果没有管理员账户，尝试使用普通账户（可能没有CREATE权限）
        if all([self.DB_HOST, self.DB_USER, self.DB_PASSWORD]):
            return f"mysql+{self.DB_DRIVER}://{self.DB_USER}:{self.DB_PASSWORD}@{self.DB_HOST}:{self.DB_PORT}"

        return None

    def validate_database_config(self) -> tuple[bool, str]:
        """
        验证数据库配置的完整性

        Returns:
            tuple[bool, str]: (是否有效, 错误信息)
        """
        # 检查是否有任何数据库配置
        has_url_config = bool(self.DATABASE_URL)
        has_param_config = all([self.DB_HOST, self.DB_USER, self.DB_PASSWORD, self.DB_NAME])

        if not has_url_config and not has_param_config:
            return False, "数据库配置缺失：请配置DATABASE_URL或独立的数据库参数(DB_HOST, DB_USER, DB_PASSWORD, DB_NAME)"

        # 如果启用自动建库，检查管理员配置
        if self.AUTO_CREATE_DATABASE and has_param_config:
            if not self.DB_ADMIN_USER or not self.DB_ADMIN_PASSWORD:
                return False, "启用自动建库时，必须配置管理员账户(DB_ADMIN_USER, DB_ADMIN_PASSWORD)"

        return True, ""

    def validate_sync_config(self) -> tuple[bool, str]:
        """
        验证同步配置的完整性

        Returns:
            tuple[bool, str]: (是否有效, 错误信息)
        """
        # 验证压缩级别
        if not (1 <= self.SYNC_COMPRESSION_LEVEL <= 9):
            return False, f"同步压缩级别必须在1-9之间，当前值: {self.SYNC_COMPRESSION_LEVEL}"

        if not (1 <= self.OFFLINE_PACKAGE_DEFAULT_COMPRESSION_LEVEL <= 9):
            return False, f"离线包默认压缩级别必须在1-9之间，当前值: {self.OFFLINE_PACKAGE_DEFAULT_COMPRESSION_LEVEL}"

        # 验证缓存大小配置
        if self.SYNC_CACHE_SIZE_MB <= 0:
            return False, f"同步缓存大小必须大于0，当前值: {self.SYNC_CACHE_SIZE_MB}MB"

        # 验证包保留天数
        if self.SYNC_PACKAGE_RETENTION_DAYS <= 0:
            return False, f"同步包保留天数必须大于0，当前值: {self.SYNC_PACKAGE_RETENTION_DAYS}"

        if self.OFFLINE_PACKAGE_DEFAULT_EXPIRY_DAYS <= 0:
            return False, f"离线包默认过期天数必须大于0，当前值: {self.OFFLINE_PACKAGE_DEFAULT_EXPIRY_DAYS}"

        # 验证缓存TTL配置
        if self.SYNC_VERSION_CACHE_TTL <= 0:
            return False, f"版本缓存TTL必须大于0，当前值: {self.SYNC_VERSION_CACHE_TTL}"

        if self.SYNC_CHANGES_CACHE_TTL <= 0:
            return False, f"变更缓存TTL必须大于0，当前值: {self.SYNC_CHANGES_CACHE_TTL}"

        # 验证离线包存储路径
        if not self.OFFLINE_PACKAGE_STORAGE_PATH:
            return False, "离线包存储路径不能为空"

        # 验证离线包最大大小
        if self.OFFLINE_PACKAGE_MAX_SIZE_MB <= 0:
            return False, f"离线包最大大小必须大于0，当前值: {self.OFFLINE_PACKAGE_MAX_SIZE_MB}MB"

        # 验证缓存配置
        cache_configs = [
            ("版本缓存", self.CACHE_VERSION_MAX_SIZE, self.CACHE_VERSION_MAX_MEMORY_MB),
            ("变更缓存", self.CACHE_CHANGES_MAX_SIZE, self.CACHE_CHANGES_MAX_MEMORY_MB),
            ("包缓存", self.CACHE_PACKAGE_MAX_SIZE, self.CACHE_PACKAGE_MAX_MEMORY_MB),
            ("同步结果缓存", self.CACHE_SYNC_RESULT_MAX_SIZE, self.CACHE_SYNC_RESULT_MAX_MEMORY_MB),
        ]

        for cache_name, max_size, max_memory in cache_configs:
            if max_size <= 0:
                return False, f"{cache_name}最大条目数必须大于0，当前值: {max_size}"
            if max_memory <= 0:
                return False, f"{cache_name}最大内存必须大于0，当前值: {max_memory}MB"

        return True, ""

    def get_sync_cache_config(self) -> dict:
        """
        获取同步缓存配置

        Returns:
            dict: 缓存配置字典
        """
        return {
            "version_cache": {
                "max_size": self.CACHE_VERSION_MAX_SIZE,
                "max_memory_mb": self.CACHE_VERSION_MAX_MEMORY_MB,
                "ttl_seconds": self.CACHE_VERSION_TTL_SECONDS,
                "cleanup_interval": self.CACHE_VERSION_CLEANUP_INTERVAL,
            },
            "changes_cache": {
                "max_size": self.CACHE_CHANGES_MAX_SIZE,
                "max_memory_mb": self.CACHE_CHANGES_MAX_MEMORY_MB,
                "ttl_seconds": self.CACHE_CHANGES_TTL_SECONDS,
                "cleanup_interval": self.CACHE_CHANGES_CLEANUP_INTERVAL,
            },
            "package_cache": {
                "max_size": self.CACHE_PACKAGE_MAX_SIZE,
                "max_memory_mb": self.CACHE_PACKAGE_MAX_MEMORY_MB,
                "ttl_seconds": self.CACHE_PACKAGE_TTL_SECONDS,
                "cleanup_interval": self.CACHE_PACKAGE_CLEANUP_INTERVAL,
            },
            "sync_result_cache": {
                "max_size": self.CACHE_SYNC_RESULT_MAX_SIZE,
                "max_memory_mb": self.CACHE_SYNC_RESULT_MAX_MEMORY_MB,
                "ttl_seconds": self.CACHE_SYNC_RESULT_TTL_SECONDS,
                "cleanup_interval": self.CACHE_SYNC_RESULT_CLEANUP_INTERVAL,
            },
        }

    def get_offline_package_config(self) -> dict:
        """
        获取离线包配置

        Returns:
            dict: 离线包配置字典
        """
        return {
            "storage_path": self.OFFLINE_PACKAGE_STORAGE_PATH,
            "max_size_mb": self.OFFLINE_PACKAGE_MAX_SIZE_MB,
            "cleanup_interval": self.OFFLINE_PACKAGE_CLEANUP_INTERVAL,
            "default_expiry_days": self.OFFLINE_PACKAGE_DEFAULT_EXPIRY_DAYS,
            "max_count": self.OFFLINE_PACKAGE_MAX_COUNT,
            "default_compression_level": self.OFFLINE_PACKAGE_DEFAULT_COMPRESSION_LEVEL,
            "generation_timeout": self.OFFLINE_PACKAGE_GENERATION_TIMEOUT,
            "download_timeout": self.OFFLINE_PACKAGE_DOWNLOAD_TIMEOUT,
            "integrity_check_enabled": self.OFFLINE_PACKAGE_INTEGRITY_CHECK_ENABLED,
        }


@lru_cache()
def get_settings() -> Settings:
    """
    Returns a cached instance of the application settings.
    Using lru_cache ensures the settings are loaded only once.
    """
    return Settings()


# A single instance for easy access, though get_settings() is preferred
# for dependency injection in FastAPI.
settings = get_settings()
